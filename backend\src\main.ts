import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import compression from 'compression';

import { AppModule } from './app.module';
import { WinstonModule } from 'nest-winston';
import { createWinstonConfig } from './config/winston.config';

async function bootstrap() {
  // 创建应用实例
  const app = await NestFactory.create(AppModule, {
    logger: WinstonModule.createLogger(createWinstonConfig()),
  });

  // 获取配置服务
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT', 3001);
  const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // 安全中间件
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }));

  // 压缩中间件
  app.use(compression());

  // CORS 配置
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', 'http://localhost:5173'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // API 前缀
  app.setGlobalPrefix('api');

  // Swagger 文档配置 (仅开发环境)
  if (nodeEnv === 'development') {
    const config = new DocumentBuilder()
      .setTitle('SeedTra API')
      .setDescription('SeedTra 实时翻译系统 API 文档')
      .setVersion('1.0')
      .addBearerAuth()
      .addTag('auth', '认证相关接口')
      .addTag('websocket', 'WebSocket 相关接口')
      .addTag('translation', '翻译相关接口')
      .addTag('health', '健康检查接口')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    console.log(`📚 Swagger 文档地址: http://localhost:${port}/api/docs`);
  }

  // 启动应用
  await app.listen(port, '0.0.0.0');

  console.log(`🚀 SeedTra 后端服务启动成功`);
  console.log(`🌐 服务地址: http://localhost:${port}`);
  console.log(`🔧 环境: ${nodeEnv}`);
  console.log(`📊 健康检查: http://localhost:${port}/api/health`);
}

// 启动应用
bootstrap().catch((error) => {
  console.error('❌ 应用启动失败:', error);
  process.exit(1);
});
