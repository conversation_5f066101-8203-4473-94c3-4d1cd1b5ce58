// SeedTra 共享类型定义

/**
 * 音频相关类型定义
 */
export interface AudioConfig {
  readonly sampleRate: number;
  readonly channels: number;
  readonly bitsPerSample: number;
}

export interface AudioChunkData {
  readonly data: ArrayBuffer;
  readonly timestamp: number;
  readonly sequenceId: number;
  readonly isLast?: boolean;
}

/**
 * ASR (语音识别) 相关类型定义
 */
export interface ASRWord {
  readonly word: string;
  readonly confidence: number;
  readonly startTime: number;
  readonly endTime: number;
}

export interface ASRResult {
  readonly sessionId: string;
  readonly type: 'partial' | 'final';
  readonly text: string;
  readonly confidence: number;
  readonly timestamp: number;
  readonly startTime?: number;
  readonly endTime?: number;
  readonly words?: ReadonlyArray<ASRWord>;
}

export interface ASRError {
  readonly sessionId: string;
  readonly errorCode: string;
  readonly errorMessage: string;
  readonly timestamp: number;
}

/**
 * 翻译相关类型定义
 */
export interface TranslationChunk {
  readonly sessionId: string;
  readonly sourceText: string;
  readonly translatedChunk: string;
  readonly isComplete: boolean;
  readonly timestamp: number;
  readonly chunkIndex: number;
}

export interface TranslationComplete {
  readonly sessionId: string;
  readonly sourceText: string;
  readonly finalTranslation: string;
  readonly latency: number;
  readonly timestamp: number;
}

export interface TranslationError {
  readonly sessionId: string;
  readonly errorCode: string;
  readonly errorMessage: string;
  readonly timestamp: number;
}

/**
 * WebSocket 通信相关类型定义
 */
export interface WebSocketConnection {
  readonly id: string;
  readonly userId: string;
  readonly isActive: boolean;
  readonly connectedAt: number;
}

export interface SocketError {
  readonly code: string;
  readonly message: string;
  readonly details?: unknown;
  readonly timestamp: number;
  readonly sessionId?: string;
}

/**
 * 会话管理相关类型定义
 */
export const enum SessionStatus {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  TRANSLATING = 'translating',
  COMPLETED = 'completed',
  ERROR = 'error',
}

export interface SessionStatusUpdate {
  readonly sessionId: string;
  readonly status: SessionStatus;
  readonly timestamp: number;
  readonly metadata?: {
    readonly asrConnected?: boolean;
    readonly llmConnected?: boolean;
    readonly queuePosition?: number;
  };
}

/**
 * 系统状态相关类型定义
 */
export interface SystemStatus {
  readonly services: {
    readonly asr: 'online' | 'offline' | 'degraded';
    readonly llm: 'online' | 'offline' | 'degraded';
    readonly database: 'online' | 'offline';
  };
  readonly load: {
    readonly cpu: number;
    readonly memory: number;
    readonly connections: number;
  };
  readonly timestamp: number;
}

/**
 * API 响应相关类型定义
 */
export interface APIResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp: number;
}

export type Result<T, E = Error> = {
  readonly success: true;
  readonly data: T;
} | {
  readonly success: false;
  readonly error: E;
};

/**
 * 用户相关类型定义
 */
export interface User {
  readonly id: string;
  readonly email: string;
  readonly createdAt: string;
  readonly updatedAt: string;
}

export interface AuthTokens {
  readonly accessToken: string;
  readonly refreshToken: string;
  readonly expiresIn: number;
}

/**
 * 翻译历史相关类型定义
 */
export interface TranslationHistory {
  readonly id: string;
  readonly sessionId: string;
  readonly userId: string;
  readonly sourceText: string;
  readonly translatedText: string;
  readonly latency: number;
  readonly timestamp: string;
  readonly metadata?: {
    readonly asrEvents?: unknown[];
    readonly llmEvents?: unknown[];
  };
}

/**
 * 常量定义
 */
export const AUDIO_CONSTANTS = {
  SAMPLE_RATE: 16000,
  CHANNELS: 1,
  BITS_PER_SAMPLE: 16,
  CHUNK_SIZE: 3200,
  BUFFER_SIZE: 8192,
} as const;

export const API_ENDPOINTS = {
  ASR_WEBSOCKET: 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async',
  LLM_HTTP: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
} as const;

export const ERROR_CODES = {
  AUTH_FAILED: 'AUTH_FAILED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  AUDIO_FORMAT_ERROR: 'AUDIO_FORMAT_ERROR',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type AudioFormat = 'pcm' | 'wav' | 'mp3';
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';
