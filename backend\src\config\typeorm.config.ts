import { TypeOrmModuleAsyncOptions } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DataSource, DataSourceOptions } from 'typeorm';

export const typeOrmConfig: TypeOrmModuleAsyncOptions = {
  imports: [ConfigModule],
  useFactory: (configService: ConfigService): DataSourceOptions => ({
    type: 'postgres',
    host: configService.get<string>('DATABASE_HOST', 'localhost'),
    port: configService.get<number>('DATABASE_PORT', 5432),
    username: configService.get<string>('DATABASE_USERNAME', 'seedtra_user'),
    password: configService.get<string>('DATABASE_PASSWORD', 'seedtra_password'),
    database: configService.get<string>('DATABASE_NAME', 'seedtra_db'),
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
    synchronize: configService.get<string>('NODE_ENV') === 'development',
    logging: configService.get<string>('NODE_ENV') === 'development',
    ssl: configService.get<string>('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
    extra: {
      max: configService.get<number>('DATABASE_MAX_CONNECTIONS', 20),
      idleTimeoutMillis: configService.get<number>('DATABASE_IDLE_TIMEOUT', 30000),
      connectionTimeoutMillis: configService.get<number>('DATABASE_CONNECTION_TIMEOUT', 60000),
    },
  }),
  inject: [ConfigService],
};

// 用于 TypeORM CLI 的数据源配置
export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USERNAME || 'seedtra_user',
  password: process.env.DATABASE_PASSWORD || 'seedtra_password',
  database: process.env.DATABASE_NAME || 'seedtra_db',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.NODE_ENV === 'development',
});
