# SeedTra 实时翻译系统 - 项目结构目录

**创建时间**: 2025-07-31 00:50:16  
**最后更新**: 2025-07-31 00:50:16  
**文档版本**: v1.0

## 📁 项目根目录结构

```
SeedTra/
├── docs/                           # 📚 项目文档目录
│   ├── 1-tasks/                   # 📋 任务清单文档
│   ├── 2-progress/                # 📈 项目进度文档
│   ├── 3-requirements/            # 📝 需求规格文档
│   ├── 4-design/                  # 🎨 设计方案文档
│   ├── 5-reports/                 # 📊 各类报告文档
│   ├── 6-api/                     # 🔌 API接口文档
│   ├── 7-standards/               # 📏 代码规范文档
│   ├── 8-architecture/            # 🏗️ 架构文档
│   └── 项目结构目录.md            # 📁 本文档
├── backend/                        # 🚀 后端服务 (NestJS)
│   ├── src/                       # 源代码目录
│   │   ├── app.module.ts          # 应用根模块
│   │   ├── main.ts                # 应用入口文件
│   │   ├── auth/                  # 🔐 认证模块
│   │   ├── websocket/             # 🔌 WebSocket模块
│   │   │   └── audio.gateway.ts   # 音频网关
│   │   ├── translation/           # 🔄 翻译编排模块
│   │   │   ├── translation.orchestration.service.ts
│   │   │   ├── asr.client.ts      # 豆包ASR客户端
│   │   │   └── llm.client.ts      # 豆包LLM客户端
│   │   ├── persistence/           # 💾 数据持久化模块
│   │   ├── config/                # ⚙️ 配置模块
│   │   └── redis/                 # 📡 Redis模块
│   ├── test/                      # 🧪 测试文件
│   ├── package.json               # 依赖配置
│   ├── tsconfig.json              # TypeScript配置
│   ├── nest-cli.json              # NestJS CLI配置
│   └── Dockerfile                 # Docker镜像配置
├── frontend/                       # 🎨 前端应用 (React)
│   ├── public/                    # 静态资源
│   │   └── pcm-processor.js       # AudioWorklet处理器
│   ├── src/                       # 源代码目录
│   │   ├── App.tsx                # 应用根组件
│   │   ├── index.tsx              # 应用入口
│   │   ├── components/            # 🧩 组件目录
│   │   │   ├── TranslationPage.tsx
│   │   │   ├── AudioInputButton.tsx
│   │   │   ├── SourceTextView.tsx
│   │   │   └── TranslatedTextView.tsx
│   │   ├── hooks/                 # 🎣 自定义Hook
│   │   │   ├── useMicrophoneProcessor.ts
│   │   │   └── useWebSocket.ts
│   │   ├── context/               # 🌐 React Context
│   │   │   └── AppContext.tsx
│   │   ├── utils/                 # 🛠️ 工具函数
│   │   └── types/                 # 📝 TypeScript类型定义
│   ├── package.json               # 依赖配置
│   ├── tsconfig.json              # TypeScript配置
│   ├── vite.config.ts             # Vite配置
│   └── Dockerfile                 # Docker镜像配置
├── shared/                         # 🤝 共享代码 (Monorepo)
│   ├── types/                     # 📝 共享类型定义
│   └── utils/                     # 🛠️ 共享工具函数
├── database/                       # 🗄️ 数据库相关
│   ├── migrations/                # 📊 数据库迁移文件
│   ├── seeds/                     # 🌱 种子数据
│   └── schema.sql                 # 数据库模式定义
├── docker/                         # 🐳 Docker配置
│   ├── docker-compose.yml         # 开发环境编排
│   ├── docker-compose.prod.yml    # 生产环境编排
│   └── nginx/                     # Nginx配置
├── scripts/                        # 📜 脚本文件
│   ├── setup.sh                   # 环境初始化脚本
│   ├── build.sh                   # 构建脚本
│   └── deploy.sh                  # 部署脚本
├── .env.example                    # 环境变量示例
├── .gitignore                      # Git忽略文件
├── README.md                       # 项目说明文档
├── package.json                    # 根目录依赖配置
└── nx.json                         # Nx工作空间配置 (可选)
```

## 📚 文档目录详细说明

### 1-tasks/ - 任务清单文档
存放项目开发过程中的任务清单和进度跟踪文档。

**命名规范**: `1-[序号]-任务清单-[模块名称]-[版本号].md`

**示例文件**:
- `1-01-任务清单-项目初始化-v1.md`
- `1-02-任务清单-后端核心服务-v1.md`
- `1-03-任务清单-前端音频处理-v1.md`
- `1-04-任务清单-数据库设计-v1.md`

### 2-progress/ - 项目进度文档
记录项目整体进度、里程碑达成情况和阶段性总结。

**主要文件**:
- `项目进度总览.md` - 整体进度跟踪
- `周报-YYYY-MM-DD.md` - 周度进度报告
- `里程碑记录.md` - 重要节点记录

### 3-requirements/ - 需求规格文档
详细的功能需求、非功能需求和业务需求文档。

**主要文件**:
- `功能需求规格书.md` - 详细功能需求
- `非功能需求规格书.md` - 性能、安全等需求
- `用户故事集.md` - 用户场景和故事
- `需求变更记录.md` - 需求变更历史

### 4-design/ - 设计方案文档
系统设计、UI设计和技术方案文档。

**主要文件**:
- `系统设计方案.md` - 整体系统设计
- `数据库设计方案.md` - 数据库结构设计
- `UI设计规范.md` - 界面设计规范
- `技术选型方案.md` - 技术栈选择依据

### 5-reports/ - 各类报告文档
测试报告、性能分析报告、问题分析报告等。

**主要文件**:
- `性能测试报告.md` - 系统性能测试结果
- `安全评估报告.md` - 安全性评估
- `问题分析报告.md` - 重要问题分析
- `上线评估报告.md` - 上线准备评估

### 6-api/ - API接口文档
详细的API接口规范和使用说明。

**主要文件**:
- `WebSocket接口规范.md` - WebSocket通信协议
- `REST API规范.md` - HTTP API接口
- `豆包API集成文档.md` - 第三方API集成
- `接口变更记录.md` - API版本变更历史

### 7-standards/ - 代码规范文档
代码编写规范、命名规范和最佳实践。

**主要文件**:
- `TypeScript编码规范.md` - TS代码规范
- `React组件规范.md` - React开发规范
- `NestJS服务规范.md` - 后端服务规范
- `Git提交规范.md` - 版本控制规范

### 8-architecture/ - 架构文档
系统架构设计、部署架构和技术架构文档。

**主要文件**:
- `系统架构设计.md` - 整体架构设计
- `部署架构方案.md` - 部署和运维架构
- `数据流设计.md` - 数据流转设计
- `扩展性设计.md` - 系统扩展性方案

## 🔄 文档维护规范

### 版本控制
- 所有文档都应纳入Git版本控制
- 重要文档变更需要创建新版本
- 使用语义化版本号：v1.0, v1.1, v2.0

### 更新频率
- **任务文档**: 每日更新
- **进度文档**: 每周更新
- **设计文档**: 按需更新
- **规范文档**: 按需更新，变更需团队评审

### 文档关联
- 相关文档之间应建立明确的引用关系
- 使用相对路径进行文档间链接
- 重要决策应在多个相关文档中体现

## 📋 文档使用指南

### 新成员入门
1. 首先阅读 `README.md` 了解项目概况
2. 查看 `3-requirements/功能需求规格书.md` 理解业务需求
3. 阅读 `8-architecture/系统架构设计.md` 了解技术架构
4. 参考 `7-standards/` 下的规范文档进行开发

### 日常开发
1. 查看 `1-tasks/` 了解当前任务安排
2. 参考 `6-api/` 进行接口开发和调用
3. 遵循 `7-standards/` 中的编码规范
4. 及时更新相关文档

### 问题排查
1. 查看 `5-reports/问题分析报告.md` 了解已知问题
2. 参考 `4-design/` 中的设计文档理解系统逻辑
3. 查看 `2-progress/` 了解问题出现的时间背景

---

**维护说明**: 本文档定义了整个项目的目录结构和文档组织方式，所有团队成员都应严格遵循这个结构进行文件组织和文档编写。

*文档版本: v1.0*  
*创建时间: 2025-07-31 00:50:16*
