# SeedTra 前端应用 Dockerfile

# 基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 开发阶段
FROM base AS development

# 安装所有依赖
RUN npm ci

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 5173

# 开发命令
CMD ["npm", "run", "dev"]

# 构建阶段
FROM base AS build

# 安装所有依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段 - 使用 Nginx 服务静态文件
FROM nginx:alpine AS production

# 复制自定义 Nginx 配置
COPY docker/nginx/frontend.conf /etc/nginx/conf.d/default.conf

# 从构建阶段复制构建产物
COPY --from=build /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
