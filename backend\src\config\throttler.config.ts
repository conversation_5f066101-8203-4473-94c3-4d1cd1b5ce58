import { ThrottlerModuleOptions } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';

export const throttlerConfig = {
  imports: [ConfigModule],
  useFactory: (configService: ConfigService): ThrottlerModuleOptions => ({
    ttl: configService.get<number>('RATE_LIMIT_WINDOW', 15) * 60, // 转换为秒
    limit: configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100),
    skipIf: () => {
      // 在开发环境中可以选择跳过限流
      return configService.get<string>('NODE_ENV') === 'development';
    },
  }),
  inject: [ConfigService],
};
