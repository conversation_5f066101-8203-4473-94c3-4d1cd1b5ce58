# SeedTra 实时翻译系统 - Git忽略文件配置

# =============================================================================
# AI助手相关文件夹 (不提交到版本控制)
# =============================================================================
.augment/
.cunzhi-memory/

# =============================================================================
# Node.js 依赖和构建产物
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 构建输出
dist/
build/
out/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# =============================================================================
# 环境配置文件
# =============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# =============================================================================
# 数据库文件
# =============================================================================
*.db
*.sqlite
*.sqlite3

# PostgreSQL
*.dump

# =============================================================================
# 日志文件
# =============================================================================
logs
*.log

# =============================================================================
# 缓存目录
# =============================================================================
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# =============================================================================
# 编辑器和IDE配置
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~

# Vim
*.tmp

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统生成的文件
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# =============================================================================
# 临时文件
# =============================================================================
*.tmp
*.temp
*.bak
*.backup
*.orig

# =============================================================================
# 测试相关
# =============================================================================
# Jest
coverage/
.nyc_output/

# =============================================================================
# TypeScript
# =============================================================================
*.tsbuildinfo

# =============================================================================
# Docker
# =============================================================================
.dockerignore

# =============================================================================
# 部署相关
# =============================================================================
.vercel
.netlify

# =============================================================================
# 安全相关 (API密钥等敏感信息)
# =============================================================================
.env.secret
secrets/
*.pem
*.key
*.crt

# =============================================================================
# 开发工具
# =============================================================================
.eslintcache
.stylelintcache

# =============================================================================
# 包管理器
# =============================================================================
# Yarn
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =============================================================================
# 特别说明: docs/ 目录不被忽略
# =============================================================================
# docs/ 目录包含项目重要文档，需要纳入版本控制
# 如果需要忽略docs目录下的特定文件，请在下方添加具体路径

# =============================================================================
# 项目特定忽略项
# =============================================================================
# 音频测试文件
*.wav
*.mp3
*.pcm
test-audio/

# 性能测试结果
benchmark-results/
performance-logs/

# 本地配置文件
config.local.json
settings.local.json
