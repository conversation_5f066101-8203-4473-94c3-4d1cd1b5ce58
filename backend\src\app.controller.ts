import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '根路径' })
  @ApiResponse({ status: 200, description: '返回应用信息' })
  getRoot(): { message: string; version: string; timestamp: string } {
    return this.appService.getAppInfo();
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '返回系统健康状态' })
  async getHealth() {
    return this.appService.getHealthStatus();
  }
}
