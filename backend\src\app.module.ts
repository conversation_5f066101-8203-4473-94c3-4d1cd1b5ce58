import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { WebsocketModule } from './websocket/websocket.module';
import { TranslationModule } from './translation/translation.module';
import { PersistenceModule } from './persistence/persistence.module';
import { RedisModule } from './redis/redis.module';

import { typeOrmConfig } from './config/typeorm.config';
import { throttlerConfig } from './config/throttler.config';

@Module({
  imports: [
    // 配置模块 - 加载环境变量
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync(typeOrmConfig),

    // 限流模块
    ThrottlerModule.forRootAsync(throttlerConfig),

    // 业务模块
    AuthModule,
    WebsocketModule,
    TranslationModule,
    PersistenceModule,
    RedisModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
