{"name": "seedtra", "version": "1.0.0", "description": "SeedTra 实时翻译系统 - 高性能、低延迟的实时语音翻译解决方案", "keywords": ["real-time", "translation", "speech-recognition", "websocket", "<PERSON><PERSON><PERSON>", "react", "typescript"], "author": "SeedTra Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/SunflowersLwtech/SeedTra.git"}, "bugs": {"url": "https://github.com/SunflowersLwtech/SeedTra/issues"}, "homepage": "https://github.com/SunflowersLwtech/SeedTra#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["backend", "frontend", "shared"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run start:dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm run start:prod", "start:frontend": "cd frontend && npm run preview", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:e2e": "cd backend && npm run test:e2e", "test:coverage": "npm run test:coverage:backend && npm run test:coverage:frontend", "test:coverage:backend": "cd backend && npm run test:cov", "test:coverage:frontend": "cd frontend && npm run test:coverage", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && npm run format", "format:frontend": "cd frontend && npm run format", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "clean": "npm run clean:backend && npm run clean:frontend && npm run clean:root", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:frontend": "cd frontend && rm -rf dist node_modules", "clean:root": "rm -rf node_modules", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "db:migrate": "cd backend && npm run migration:run", "db:seed": "cd backend && npm run seed:run", "db:reset": "cd backend && npm run migration:revert && npm run migration:run && npm run seed:run", "setup": "npm run install:all && npm run db:migrate && npm run db:seed", "setup:docker": "docker-compose up -d postgres redis && npm run db:migrate && npm run db:seed", "docs:serve": "cd docs && python -m http.server 8080", "health": "curl -f http://localhost:3001/health || exit 1", "precommit": "npm run lint && npm run type-check && npm run test", "prepare": "husky install"}, "devDependencies": {"@types/node": "^20.0.0", "concurrently": "^8.2.0", "husky": "^8.0.3", "lint-staged": "^13.2.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}