# SeedTra 实时翻译系统 - 环境变量配置模板
# 复制此文件为 .env 并填入实际配置值

# =============================================================================
# 应用基础配置
# =============================================================================
NODE_ENV=development
PORT=3001
APP_NAME=SeedTra
APP_VERSION=1.0.0

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL 主数据库
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=seedtra_user
DATABASE_PASSWORD=your_database_password
DATABASE_NAME=seedtra_db
DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}

# 数据库连接池配置
DATABASE_MAX_CONNECTIONS=20
DATABASE_IDLE_TIMEOUT=30000
DATABASE_CONNECTION_TIMEOUT=60000

# =============================================================================
# Redis 配置
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# Redis 连接配置
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECTION_TIMEOUT=5000

# =============================================================================
# JWT 认证配置
# =============================================================================
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_token_secret
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# 豆包 API 配置
# =============================================================================
# 豆包 ASR (语音识别) 配置
DOUBAO_ASR_API_KEY=your_doubao_asr_api_key
DOUBAO_ASR_APP_ID=your_doubao_asr_app_id
DOUBAO_ASR_WEBSOCKET_URL=wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async
DOUBAO_ASR_CLUSTER=volcengine_streaming_common

# 豆包 LLM (大语言模型) 配置
DOUBAO_LLM_API_KEY=your_doubao_llm_api_key
DOUBAO_LLM_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DOUBAO_LLM_MODEL=doubao-lite-4k
DOUBAO_LLM_MAX_TOKENS=2048
DOUBAO_LLM_TEMPERATURE=0.3

# =============================================================================
# WebSocket 配置
# =============================================================================
WEBSOCKET_PORT=3001
WEBSOCKET_CORS_ORIGIN=http://localhost:5173
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000

# =============================================================================
# 音频处理配置
# =============================================================================
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_BITS_PER_SAMPLE=16
AUDIO_CHUNK_SIZE=3200
AUDIO_BUFFER_SIZE=8192

# =============================================================================
# 翻译配置
# =============================================================================
# 智能触发配置
TRANSLATION_TRIGGER_DELAY=1500
TRANSLATION_MIN_CONFIDENCE=0.8
TRANSLATION_MIN_TEXT_LENGTH=5
TRANSLATION_MAX_CONCURRENT=10

# 翻译超时配置
TRANSLATION_TIMEOUT=10000
TRANSLATION_RETRY_ATTEMPTS=3
TRANSLATION_RETRY_DELAY=1000

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=./logs/app.log
LOG_MAX_FILE_SIZE=10MB
LOG_MAX_FILES=5

# =============================================================================
# 安全配置
# =============================================================================
# CORS 配置
CORS_ORIGIN=http://localhost:5173
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_CREDENTIALS=true

# 限流配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# 安全头配置
HELMET_ENABLED=true
CSRF_PROTECTION=false

# =============================================================================
# 监控配置
# =============================================================================
# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_DATABASE=true
HEALTH_CHECK_REDIS=true
HEALTH_CHECK_EXTERNAL_APIS=true

# 指标收集配置
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# =============================================================================
# 文件上传配置
# =============================================================================
UPLOAD_MAX_FILE_SIZE=10MB
UPLOAD_ALLOWED_TYPES=audio/wav,audio/mp3,audio/pcm
UPLOAD_DESTINATION=./uploads

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000
CACHE_CHECK_PERIOD=600

# =============================================================================
# 开发环境配置
# =============================================================================
# 热重载配置
HOT_RELOAD=true
WATCH_FILES=true

# 调试配置
DEBUG_MODE=false
VERBOSE_LOGGING=false

# API 文档配置
SWAGGER_ENABLED=true
SWAGGER_PATH=/api/docs

# =============================================================================
# 生产环境配置 (仅在生产环境使用)
# =============================================================================
# SSL 配置
SSL_ENABLED=false
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# 集群配置
CLUSTER_MODE=false
CLUSTER_WORKERS=auto

# 外部服务配置
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRIES=3

# =============================================================================
# Docker 配置 (Docker 环境使用)
# =============================================================================
DOCKER_DATABASE_HOST=postgres
DOCKER_REDIS_HOST=redis
DOCKER_NETWORK=seedtra_network

# =============================================================================
# 测试环境配置
# =============================================================================
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5433/seedtra_test
TEST_REDIS_URL=redis://localhost:6380/1
TEST_JWT_SECRET=test_jwt_secret
TEST_LOG_LEVEL=error
