# SeedTra 实时翻译系统 - WebSocket接口规范

**创建时间**: 2025-07-31 00:50:16  
**最后更新**: 2025-07-31 00:50:16  
**文档版本**: v1.0  
**API版本**: v1

## 📋 接口概述

### 协议说明
SeedTra系统使用WebSocket协议进行客户端与服务器之间的实时双向通信，基于Socket.IO库实现，支持自动重连、心跳检测等特性。

### 连接信息
- **协议**: WebSocket (WSS)
- **端口**: 3001
- **路径**: `/socket.io/`
- **库**: Socket.IO v4.x
- **传输方式**: websocket, polling (fallback)

### 认证方式
```typescript
// 连接时携带JWT Token
const socket = io('wss://api.seedtra.com', {
  auth: {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  },
  transports: ['websocket']
});
```

## 🔌 连接管理

### 连接建立
```typescript
// 客户端连接示例
import { io, Socket } from 'socket.io-client';

const socket: Socket = io('wss://api.seedtra.com:3001', {
  auth: {
    token: localStorage.getItem('jwt_token')
  },
  transports: ['websocket'],
  timeout: 5000,
  retries: 3
});

// 连接成功
socket.on('connect', () => {
  console.log('Connected to server:', socket.id);
});

// 连接失败
socket.on('connect_error', (error) => {
  console.error('Connection failed:', error.message);
});
```

### 连接状态事件

#### `connect`
**描述**: 连接成功建立
```typescript
socket.on('connect', () => {
  // 连接成功处理逻辑
  console.log('Socket connected:', socket.id);
});
```

#### `disconnect`
**描述**: 连接断开
```typescript
socket.on('disconnect', (reason: string) => {
  console.log('Socket disconnected:', reason);
  // 可能的原因:
  // - 'io server disconnect': 服务器主动断开
  // - 'io client disconnect': 客户端主动断开
  // - 'ping timeout': 心跳超时
  // - 'transport close': 传输层关闭
});
```

#### `reconnect`
**描述**: 重连成功
```typescript
socket.on('reconnect', (attemptNumber: number) => {
  console.log('Reconnected after', attemptNumber, 'attempts');
});
```

## 🎤 音频数据传输

### 音频块上传

#### `audio-chunk`
**方向**: 客户端 → 服务器  
**描述**: 发送音频数据块到服务器

**数据格式**:
```typescript
interface AudioChunkData {
  data: ArrayBuffer;        // 音频数据 (16kHz, 16-bit PCM)
  timestamp: number;        // 时间戳 (毫秒)
  sequenceId: number;       // 序列号
  isLast?: boolean;         // 是否为最后一块
}
```

**发送示例**:
```typescript
// 发送音频数据块
const audioData: ArrayBuffer = pcmBuffer.buffer;
socket.emit('audio-chunk', {
  data: audioData,
  timestamp: Date.now(),
  sequenceId: chunkCounter++,
  isLast: false
});
```

**服务器响应**:
```typescript
// 服务器确认接收
socket.on('audio-chunk-ack', (data: { sequenceId: number; status: 'received' | 'error' }) => {
  if (data.status === 'received') {
    console.log('Audio chunk', data.sequenceId, 'received');
  }
});
```

### 录音控制

#### `start-recording`
**方向**: 客户端 → 服务器  
**描述**: 开始录音会话

```typescript
socket.emit('start-recording', {
  sessionId: uuidv4(),
  audioConfig: {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16
  }
});
```

#### `stop-recording`
**方向**: 客户端 → 服务器  
**描述**: 结束录音会话

```typescript
socket.emit('stop-recording', {
  sessionId: currentSessionId
});
```

## 📝 语音识别结果

### ASR结果推送

#### `asr-result`
**方向**: 服务器 → 客户端  
**描述**: 推送语音识别结果

**数据格式**:
```typescript
interface ASRResult {
  sessionId: string;
  type: 'partial' | 'final';    // 中间结果或最终结果
  text: string;                 // 识别文本
  confidence: number;           // 置信度 (0-1)
  timestamp: number;            // 时间戳
  startTime?: number;           // 开始时间 (毫秒)
  endTime?: number;             // 结束时间 (毫秒)
  words?: Array<{               // 词级别信息
    word: string;
    confidence: number;
    startTime: number;
    endTime: number;
  }>;
}
```

**接收示例**:
```typescript
socket.on('asr-result', (result: ASRResult) => {
  if (result.type === 'partial') {
    // 更新临时显示文本
    updatePartialText(result.text);
  } else if (result.type === 'final') {
    // 确认最终文本
    confirmFinalText(result.text);
    // 可能触发翻译
  }
});
```

### ASR错误处理

#### `asr-error`
**方向**: 服务器 → 客户端  
**描述**: ASR服务错误通知

```typescript
interface ASRError {
  sessionId: string;
  errorCode: string;
  errorMessage: string;
  timestamp: number;
}

socket.on('asr-error', (error: ASRError) => {
  console.error('ASR Error:', error.errorMessage);
  // 错误处理逻辑
  handleASRError(error);
});
```

## 🔄 翻译结果

### 翻译结果推送

#### `translation-chunk`
**方向**: 服务器 → 客户端  
**描述**: 推送流式翻译结果

**数据格式**:
```typescript
interface TranslationChunk {
  sessionId: string;
  sourceText: string;          // 原文
  translatedChunk: string;     // 翻译片段
  isComplete: boolean;         // 是否完成
  timestamp: number;           // 时间戳
  chunkIndex: number;          // 片段索引
}
```

**接收示例**:
```typescript
socket.on('translation-chunk', (chunk: TranslationChunk) => {
  if (!chunk.isComplete) {
    // 追加翻译片段
    appendTranslationChunk(chunk.translatedChunk);
  } else {
    // 翻译完成
    finalizeTranslation(chunk.sessionId);
  }
});
```

#### `translation-complete`
**方向**: 服务器 → 客户端  
**描述**: 翻译完成通知

```typescript
interface TranslationComplete {
  sessionId: string;
  sourceText: string;
  finalTranslation: string;
  latency: number;             // 翻译延迟 (毫秒)
  timestamp: number;
}

socket.on('translation-complete', (result: TranslationComplete) => {
  console.log('Translation completed in', result.latency, 'ms');
  saveTranslationHistory(result);
});
```

### 翻译控制

#### `cancel-translation`
**方向**: 客户端 → 服务器  
**描述**: 取消当前翻译请求

```typescript
socket.emit('cancel-translation', {
  sessionId: currentSessionId,
  reason: 'user_cancelled'
});
```

## ⚠️ 错误处理

### 通用错误格式

#### `error`
**方向**: 服务器 → 客户端  
**描述**: 通用错误通知

```typescript
interface SocketError {
  code: string;                // 错误代码
  message: string;             // 错误消息
  details?: any;               // 错误详情
  timestamp: number;           // 时间戳
  sessionId?: string;          // 会话ID (如果适用)
}

socket.on('error', (error: SocketError) => {
  switch (error.code) {
    case 'AUTH_FAILED':
      // 认证失败，重新登录
      redirectToLogin();
      break;
    case 'RATE_LIMIT_EXCEEDED':
      // 频率限制，暂停请求
      pauseRequests();
      break;
    case 'SERVICE_UNAVAILABLE':
      // 服务不可用，显示错误提示
      showServiceError();
      break;
    default:
      console.error('Unknown error:', error);
  }
});
```

### 错误代码定义

| 错误代码 | 描述 | 处理建议 |
|----------|------|----------|
| `AUTH_FAILED` | 认证失败 | 重新登录 |
| `INVALID_TOKEN` | Token无效 | 刷新Token |
| `RATE_LIMIT_EXCEEDED` | 频率限制 | 延迟重试 |
| `SERVICE_UNAVAILABLE` | 服务不可用 | 稍后重试 |
| `AUDIO_FORMAT_ERROR` | 音频格式错误 | 检查音频配置 |
| `SESSION_EXPIRED` | 会话过期 | 重新开始会话 |
| `INTERNAL_ERROR` | 内部错误 | 联系技术支持 |

## 📊 状态管理

### 会话状态

#### `session-status`
**方向**: 服务器 → 客户端  
**描述**: 会话状态更新

```typescript
interface SessionStatus {
  sessionId: string;
  status: 'idle' | 'listening' | 'processing' | 'translating' | 'completed' | 'error';
  timestamp: number;
  metadata?: {
    asrConnected?: boolean;
    llmConnected?: boolean;
    queuePosition?: number;
  };
}

socket.on('session-status', (status: SessionStatus) => {
  updateUIStatus(status.status);
  
  if (status.metadata?.queuePosition) {
    showQueuePosition(status.metadata.queuePosition);
  }
});
```

### 系统状态

#### `system-status`
**方向**: 服务器 → 客户端  
**描述**: 系统状态通知

```typescript
interface SystemStatus {
  services: {
    asr: 'online' | 'offline' | 'degraded';
    llm: 'online' | 'offline' | 'degraded';
    database: 'online' | 'offline';
  };
  load: {
    cpu: number;              // CPU使用率 (0-100)
    memory: number;           // 内存使用率 (0-100)
    connections: number;      // 当前连接数
  };
  timestamp: number;
}

socket.on('system-status', (status: SystemStatus) => {
  if (status.services.asr === 'offline') {
    showServiceAlert('语音识别服务暂时不可用');
  }
});
```

## 🔧 客户端实现示例

### 完整连接管理类

```typescript
class SeedTraWebSocket {
  private socket: Socket;
  private currentSessionId: string | null = null;
  
  constructor(private token: string) {
    this.initializeSocket();
  }
  
  private initializeSocket() {
    this.socket = io('wss://api.seedtra.com:3001', {
      auth: { token: this.token },
      transports: ['websocket']
    });
    
    this.setupEventHandlers();
  }
  
  private setupEventHandlers() {
    this.socket.on('connect', this.onConnect.bind(this));
    this.socket.on('disconnect', this.onDisconnect.bind(this));
    this.socket.on('asr-result', this.onASRResult.bind(this));
    this.socket.on('translation-chunk', this.onTranslationChunk.bind(this));
    this.socket.on('error', this.onError.bind(this));
  }
  
  public startRecording(): string {
    this.currentSessionId = uuidv4();
    this.socket.emit('start-recording', {
      sessionId: this.currentSessionId,
      audioConfig: {
        sampleRate: 16000,
        channels: 1,
        bitsPerSample: 16
      }
    });
    return this.currentSessionId;
  }
  
  public sendAudioChunk(audioData: ArrayBuffer, sequenceId: number) {
    if (!this.currentSessionId) return;
    
    this.socket.emit('audio-chunk', {
      data: audioData,
      timestamp: Date.now(),
      sequenceId,
      isLast: false
    });
  }
  
  public stopRecording() {
    if (!this.currentSessionId) return;
    
    this.socket.emit('stop-recording', {
      sessionId: this.currentSessionId
    });
    this.currentSessionId = null;
  }
  
  private onConnect() {
    console.log('WebSocket connected');
  }
  
  private onDisconnect(reason: string) {
    console.log('WebSocket disconnected:', reason);
  }
  
  private onASRResult(result: ASRResult) {
    // 处理ASR结果
  }
  
  private onTranslationChunk(chunk: TranslationChunk) {
    // 处理翻译结果
  }
  
  private onError(error: SocketError) {
    // 处理错误
  }
}
```

---

**维护说明**: 本接口规范定义了客户端与服务器之间的WebSocket通信协议，任何接口变更都需要保持向后兼容性，并及时更新客户端SDK。

*文档版本: v1.0*  
*创建时间: 2025-07-31 00:50:16*
