import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private readonly configService: ConfigService) {}

  getAppInfo(): { message: string; version: string; timestamp: string } {
    return {
      message: 'SeedTra 实时翻译系统后端服务',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
    };
  }

  async getHealthStatus() {
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: nodeEnv,
      uptime: `${Math.floor(uptime)}s`,
      memory: {
        used: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        total: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      },
      services: {
        database: 'checking', // TODO: 实际检查数据库连接
        redis: 'checking',    // TODO: 实际检查Redis连接
        asr: 'checking',      // TODO: 实际检查ASR服务
        llm: 'checking',      // TODO: 实际检查LLM服务
      },
    };
  }
}
