version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: seedtra_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: seedtra_db
      POSTGRES_USER: seedtra_user
      POSTGRES_PASSWORD: seedtra_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - seedtra_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: seedtra_redis
    restart: unless-stopped
    command: redis-server --requirepass seedtra_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - seedtra_network

  # 后端服务 (开发环境)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: seedtra_backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: seedtra_user
      DATABASE_PASSWORD: seedtra_password
      DATABASE_NAME: seedtra_db
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: seedtra_redis_password
    ports:
      - "3001:3001"
      - "9229:9229" # Debug port
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - seedtra_network
    command: npm run start:dev

  # 前端服务 (开发环境)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: seedtra_frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:3001
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - seedtra_network
    command: npm run dev

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: seedtra_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - backend
      - frontend
    networks:
      - seedtra_network
    profiles:
      - nginx

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  seedtra_network:
    driver: bridge
