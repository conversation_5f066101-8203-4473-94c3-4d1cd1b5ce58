#!/bin/bash

# SeedTra 项目构建脚本
# 用于构建生产版本

set -e

echo "🏗️  SeedTra 项目构建开始..."

# 清理之前的构建产物
echo "🧹 清理构建产物..."
rm -rf backend/dist
rm -rf frontend/dist

# 类型检查
echo "🔍 执行类型检查..."
echo "  检查后端类型..."
cd backend && npm run type-check
cd ..

echo "  检查前端类型..."
cd frontend && npm run type-check
cd ..

echo "✅ 类型检查通过"

# 代码检查
echo "🔍 执行代码检查..."
npm run lint

echo "✅ 代码检查通过"

# 运行测试
echo "🧪 运行测试..."
npm run test

echo "✅ 测试通过"

# 构建共享模块
echo "📦 构建共享模块..."
cd shared
npm run build
cd ..

# 构建后端
echo "📦 构建后端..."
cd backend
npm run build
cd ..

echo "✅ 后端构建完成"

# 构建前端
echo "📦 构建前端..."
cd frontend
npm run build
cd ..

echo "✅ 前端构建完成"

# 生成构建报告
echo "📊 生成构建报告..."
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
BUILD_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

cat > build-report.json << EOF
{
  "buildTime": "$BUILD_TIME",
  "gitHash": "$BUILD_HASH",
  "nodeVersion": "$(node -v)",
  "npmVersion": "$(npm -v)",
  "backend": {
    "distSize": "$(du -sh backend/dist 2>/dev/null | cut -f1 || echo 'unknown')"
  },
  "frontend": {
    "distSize": "$(du -sh frontend/dist 2>/dev/null | cut -f1 || echo 'unknown')"
  }
}
EOF

echo "✅ 构建报告已生成: build-report.json"

echo ""
echo "🎉 SeedTra 项目构建完成！"
echo ""
echo "📋 构建产物："
echo "   后端: backend/dist/"
echo "   前端: frontend/dist/"
echo ""
echo "🚀 部署命令："
echo "   npm run docker:prod"
echo ""
