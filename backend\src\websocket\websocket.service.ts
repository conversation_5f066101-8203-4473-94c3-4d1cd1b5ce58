import { Injectable, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';

@Injectable()
export class WebsocketService {
  private readonly logger = new Logger(WebsocketService.name);
  private readonly connectedClients = new Map<string, Socket>();

  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`当前连接数: ${this.connectedClients.size}`);
  }

  handleDisconnection(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`当前连接数: ${this.connectedClients.size}`);
  }

  getConnectionCount(): number {
    return this.connectedClients.size;
  }

  getConnectedClients(): Map<string, Socket> {
    return this.connectedClients;
  }
}
