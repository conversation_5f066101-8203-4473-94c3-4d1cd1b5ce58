# SeedTra 实时翻译系统 - 系统架构设计

**创建时间**: 2025-07-31 00:50:16  
**最后更新**: 2025-07-31 00:50:16  
**文档版本**: v1.0  
**架构师**: [待分配]

## 📋 架构概述

### 设计目标
构建一个高性能、低延迟、高并发的实时翻译系统，实现端到端延迟小于500毫秒的语音翻译服务。

### 核心原则
- **低延迟优先**: 所有架构决策以最小化延迟为首要目标
- **流式处理**: 采用流式数据处理模式，避免批处理延迟
- **水平扩展**: 支持无状态服务的水平扩展
- **故障隔离**: 组件间松耦合，单点故障不影响整体服务

## 🏗️ 整体架构

### 架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        客户端层 (React)                          │
├─────────────────────────────────────────────────────────────────┤
│  AudioWorklet  │  WebSocket Client  │  UI Components  │  State   │
│  音频处理      │  实时通信          │  界面组件       │  状态管理 │
└─────────────────────────────────────────────────────────────────┘
                                │
                        WebSocket (WSS)
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      网关层 (NestJS)                             │
├─────────────────────────────────────────────────────────────────┤
│  AudioGateway  │  Auth Guard  │  Rate Limiter  │  Load Balancer │
│  音频网关      │  认证守卫    │  限流器        │  负载均衡      │
└─────────────────────────────────────────────────────────────────┘
                                │
                        内部服务调用
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      业务层 (NestJS)                             │
├─────────────────────────────────────────────────────────────────┤
│  Translation    │  ASR Client  │  LLM Client  │  Session Mgmt   │
│  Orchestration  │  语音识别    │  大语言模型  │  会话管理       │
└─────────────────────────────────────────────────────────────────┘
                                │
                    外部API调用 + 内部存储
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      数据层                                      │
├─────────────────────────────────────────────────────────────────┤
│  PostgreSQL    │  Redis       │  豆包ASR     │  豆包LLM        │
│  持久化存储    │  缓存/状态   │  语音识别    │  文本翻译       │
└─────────────────────────────────────────────────────────────────┘
```

### 技术栈选择

| 层级 | 技术选择 | 选择理由 |
|------|----------|----------|
| 前端 | React + TypeScript | 成熟的生态系统，强类型支持 |
| 音频处理 | AudioWorklet | 高性能，独立线程处理 |
| 后端框架 | NestJS + TypeScript | 企业级框架，装饰器支持，易于扩展 |
| 实时通信 | Socket.IO | 成熟的WebSocket库，自动重连 |
| 数据库 | PostgreSQL | 关系型数据库，JSONB支持 |
| 缓存 | Redis | 高性能内存数据库，Pub/Sub支持 |
| 容器化 | Docker + Docker Compose | 环境一致性，易于部署 |

## 🔄 数据流设计

### 端到端数据流

```
用户语音 → AudioWorklet → WebSocket → AudioGateway → 
TranslationOrchestration → ASR API → 智能触发 → 
LLM API → 流式返回 → WebSocket → 客户端渲染
```

### 详细流程

1. **音频捕获阶段**
   - AudioWorklet在独立线程捕获音频
   - 降采样至16kHz，转换为PCM格式
   - 200ms音频块通过WebSocket发送

2. **语音识别阶段**
   - AudioGateway接收音频块
   - 转发至豆包ASR WebSocket连接
   - 实时接收识别结果（中间+最终）

3. **智能触发阶段**
   - TranslationOrchestration分析ASR结果
   - 应用触发算法（停顿检测、标点符号、稳定性）
   - 决定是否启动翻译

4. **翻译处理阶段**
   - 向豆包LLM发送流式翻译请求
   - 支持AbortController取消机制
   - 实时解析SSE响应

5. **结果返回阶段**
   - 通过WebSocket推送翻译块
   - 客户端流式渲染
   - 异步持久化到数据库

## 🏛️ 服务架构

### 微服务划分

#### 1. 网关服务 (Gateway Service)
**职责**: WebSocket连接管理、认证、限流
```typescript
@WebSocketGateway()
export class AudioGateway {
  // WebSocket连接生命周期管理
  // 音频数据接收和转发
  // 客户端认证和授权
}
```

#### 2. 翻译编排服务 (Translation Orchestration Service)
**职责**: 核心业务逻辑、ASR/LLM集成、智能触发
```typescript
@Injectable()
export class TranslationOrchestrationService {
  // ASR客户端管理
  // LLM流式调用
  // 智能触发算法
  // 会话状态管理
}
```

#### 3. 持久化服务 (Persistence Service)
**职责**: 数据库操作、历史记录管理
```typescript
@Injectable()
export class PersistenceService {
  // 翻译历史存储
  // 用户数据管理
  // 会话记录持久化
}
```

#### 4. 配置服务 (Config Service)
**职责**: 环境配置、API密钥管理
```typescript
@Injectable()
export class ConfigService {
  // 环境变量管理
  // API密钥配置
  // 系统参数设置
}
```

### 服务间通信

#### 内部通信
- **同步调用**: 直接依赖注入
- **异步通信**: Redis Pub/Sub
- **状态共享**: Redis存储

#### 外部通信
- **豆包ASR**: WebSocket (wss://)
- **豆包LLM**: HTTPS (流式)
- **客户端**: WebSocket (Socket.IO)

## 📊 数据架构

### 数据库设计

#### PostgreSQL 主数据库
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    hashed_password TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 翻译会话表
CREATE TABLE translation_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    start_time TIMESTAMPTZ DEFAULT NOW(),
    end_time TIMESTAMPTZ,
    metadata JSONB
);

-- 翻译历史表
CREATE TABLE translation_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES translation_sessions(id),
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    source_text_final TEXT NOT NULL,
    translated_text_final TEXT NOT NULL,
    asr_events JSONB,
    llm_events JSONB,
    latency_ms INTEGER
);
```

#### Redis 缓存设计
```
# 连接状态管理
user:connections -> Hash {userId: "instanceId:socketId"}

# 会话状态
session:{sessionId} -> Hash {
  "status": "active|idle",
  "lastActivity": timestamp,
  "asrConnectionId": connectionId
}

# 发布订阅频道
translation-events -> Channel for broadcasting results
system-events -> Channel for system notifications
```

### 数据一致性策略

#### 最终一致性
- 翻译历史异步写入PostgreSQL
- Redis状态允许短暂不一致
- 客户端重连时状态重建

#### 事务处理
- 用户注册：强一致性事务
- 翻译记录：异步批量写入
- 会话管理：Redis原子操作

## 🚀 性能优化

### 延迟优化策略

#### 1. 网络层优化
- WebSocket长连接复用
- 启用permessage-deflate压缩
- CDN加速静态资源

#### 2. 应用层优化
- 连接池管理（数据库、Redis）
- 智能触发算法减少API调用
- 流式处理避免缓冲延迟

#### 3. 数据层优化
- Redis内存缓存热点数据
- PostgreSQL索引优化
- JSONB字段GIN索引

### 并发处理策略

#### 1. 水平扩展
- 无状态服务设计
- Redis Pub/Sub解耦实例
- 负载均衡器分发请求

#### 2. 资源管理
- 连接池限制
- 内存使用监控
- CPU密集任务异步化

#### 3. 背压处理
- AbortController取消过期请求
- 队列长度限制
- 优雅降级机制

## 🔒 安全架构

### 认证与授权
```typescript
// JWT认证策略
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  // Token验证
  // 用户权限检查
  // 会话管理
}
```

### 数据安全
- HTTPS/WSS加密传输
- 数据库连接加密
- 敏感信息环境变量存储
- API密钥轮换机制

### 访问控制
- 基于角色的权限控制(RBAC)
- API访问频率限制
- IP白名单机制
- 审计日志记录

## 📈 监控与运维

### 监控指标
```typescript
// 关键性能指标
interface Metrics {
  latency: {
    endToEnd: number;    // 端到端延迟
    asrResponse: number; // ASR响应时间
    llmResponse: number; // LLM响应时间
  };
  throughput: {
    connectionsActive: number; // 活跃连接数
    requestsPerSecond: number; // 每秒请求数
  };
  errors: {
    asrFailures: number;  // ASR失败次数
    llmFailures: number;  // LLM失败次数
    connectionDrops: number; // 连接断开次数
  };
}
```

### 日志策略
- 结构化日志（JSON格式）
- 分级日志（ERROR/WARN/INFO/DEBUG）
- 分布式链路追踪
- 敏感信息脱敏

### 部署架构
```yaml
# Docker Compose 生产配置
version: '3.8'
services:
  nginx:          # 反向代理
  backend:        # NestJS应用（多实例）
  frontend:       # React应用
  postgres:       # 主数据库
  redis:          # 缓存和消息队列
  monitoring:     # 监控服务
```

## 🔄 扩展性设计

### 水平扩展
- 后端服务无状态设计
- 数据库读写分离
- Redis集群部署
- 微服务架构支持

### 垂直扩展
- 资源配置参数化
- 性能瓶颈监控
- 自动扩缩容支持

### 功能扩展
- 插件化架构设计
- API版本管理
- 多语言支持预留
- 第三方服务集成接口

---

**维护说明**: 本架构设计文档是系统实现的核心指导，任何架构变更都需要经过技术委员会评审，并及时更新相关的设计和实现文档。

*文档版本: v1.0*  
*创建时间: 2025-07-31 00:50:16*
