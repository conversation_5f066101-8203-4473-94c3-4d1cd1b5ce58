#!/bin/bash

# SeedTra 项目环境初始化脚本
# 用于快速搭建开发环境

set -e

echo "🚀 SeedTra 项目环境初始化开始..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18.0.0 或更高版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    echo "❌ Node.js 版本过低，当前版本: $NODE_VERSION，需要: $REQUIRED_VERSION 或更高"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $NODE_VERSION"

# 检查 Docker 和 Docker Compose
echo "📋 检查 Docker 环境..."
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "✅ Docker 环境检查通过"
    DOCKER_AVAILABLE=true
else
    echo "⚠️  Docker 未安装，将跳过 Docker 相关步骤"
    DOCKER_AVAILABLE=false
fi

# 创建环境变量文件
echo "📋 创建环境变量文件..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
else
    echo "⚠️  .env 文件已存在，跳过创建"
fi

# 安装根目录依赖
echo "📦 安装根目录依赖..."
npm install

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
npm install
cd ..

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
npm install
cd ..

# 安装共享模块依赖
echo "📦 安装共享模块依赖..."
cd shared
npm install
cd ..

# 启动数据库服务 (如果 Docker 可用)
if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "🐳 启动数据库服务..."
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 10
    
    # 运行数据库迁移
    echo "🗄️  运行数据库迁移..."
    cd backend
    npm run migration:run || echo "⚠️  数据库迁移失败，请检查数据库连接"
    cd ..
else
    echo "⚠️  请手动启动 PostgreSQL 和 Redis 服务"
    echo "   PostgreSQL: localhost:5432"
    echo "   Redis: localhost:6379"
fi

# 设置 Git hooks (如果是 Git 仓库)
if [ -d .git ]; then
    echo "🔧 设置 Git hooks..."
    npx husky install
    echo "✅ Git hooks 设置完成"
fi

echo ""
echo "🎉 SeedTra 项目环境初始化完成！"
echo ""
echo "📋 下一步操作："
echo "   1. 编辑 .env 文件，配置必要的环境变量"
echo "   2. 启动开发服务："
echo "      npm run dev"
echo ""
echo "🔗 访问地址："
echo "   前端: http://localhost:5173"
echo "   后端: http://localhost:3001"
echo ""
echo "📚 更多信息请查看 README.md"
