import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { TranslationService } from './translation.service';

@ApiTags('translation')
@Controller('translation')
export class TranslationController {
  constructor(private readonly translationService: TranslationService) {}

  @Get('status')
  @ApiOperation({ summary: '翻译服务状态检查' })
  getTranslationStatus() {
    return this.translationService.getTranslationStatus();
  }
}
