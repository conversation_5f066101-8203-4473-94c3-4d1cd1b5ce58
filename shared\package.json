{"name": "@seedtra/shared", "version": "1.0.0", "description": "SeedTra 共享代码 - 前后端共享的类型定义和工具函数", "author": "SeedTra Team", "private": true, "license": "MIT", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "type-check": "tsc --noEmit", "lint": "eslint \"**/*.ts\" --fix", "format": "prettier --write \"**/*.{ts,json,md}\""}, "dependencies": {}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "prettier": "^3.0.0", "typescript": "^5.0.2"}}