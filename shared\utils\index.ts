// SeedTra 共享工具函数

/**
 * 延迟执行函数
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 生成唯一ID
 * @returns 唯一标识符
 */
export const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @param format 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatTimestamp = (
  timestamp: number,
  format: 'datetime' | 'time' | 'date' = 'datetime'
): string => {
  const date = new Date(timestamp);
  
  switch (format) {
    case 'datetime':
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    case 'time':
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    case 'date':
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
    default:
      return date.toISOString();
  }
};

/**
 * 计算延迟时间
 * @param startTime 开始时间戳
 * @param endTime 结束时间戳
 * @returns 延迟毫秒数
 */
export const calculateLatency = (startTime: number, endTime: number): number => {
  return Math.max(0, endTime - startTime);
};

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
};

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

/**
 * 重试函数
 * @param fn 要重试的异步函数
 * @param maxRetries 最大重试次数
 * @param retryDelay 重试延迟
 * @returns Promise
 */
export const retry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (i < maxRetries) {
        await delay(retryDelay * Math.pow(2, i)); // 指数退避
      }
    }
  }
  
  throw lastError!;
};

/**
 * 验证音频格式
 * @param buffer 音频数据缓冲区
 * @returns 是否为有效的PCM格式
 */
export const validateAudioFormat = (buffer: ArrayBuffer): boolean => {
  // 基本验证：检查缓冲区大小是否合理
  if (buffer.byteLength === 0 || buffer.byteLength > 1024 * 1024) {
    return false;
  }
  
  // 检查是否为偶数字节（16-bit PCM）
  return buffer.byteLength % 2 === 0;
};

/**
 * 计算音频时长
 * @param bufferSize 缓冲区大小（字节）
 * @param sampleRate 采样率
 * @param channels 声道数
 * @param bitsPerSample 每样本位数
 * @returns 音频时长（秒）
 */
export const calculateAudioDuration = (
  bufferSize: number,
  sampleRate: number,
  channels: number,
  bitsPerSample: number
): number => {
  const bytesPerSample = bitsPerSample / 8;
  const totalSamples = bufferSize / (bytesPerSample * channels);
  return totalSamples / sampleRate;
};

/**
 * 安全的JSON解析
 * @param jsonString JSON字符串
 * @param defaultValue 默认值
 * @returns 解析结果或默认值
 */
export const safeJsonParse = <T>(jsonString: string, defaultValue: T): T => {
  try {
    return JSON.parse(jsonString) as T;
  } catch {
    return defaultValue;
  }
};

/**
 * 检查是否为有效的会话ID
 * @param sessionId 会话ID
 * @returns 是否有效
 */
export const isValidSessionId = (sessionId: string): boolean => {
  return typeof sessionId === 'string' && sessionId.length > 0 && sessionId.length <= 100;
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 创建AbortController的超时版本
 * @param timeoutMs 超时毫秒数
 * @returns AbortController实例
 */
export const createTimeoutController = (timeoutMs: number): AbortController => {
  const controller = new AbortController();
  
  setTimeout(() => {
    controller.abort();
  }, timeoutMs);
  
  return controller;
};

/**
 * 类型守卫：检查是否为错误对象
 * @param value 要检查的值
 * @returns 是否为Error实例
 */
export const isError = (value: unknown): value is Error => {
  return value instanceof Error;
};

/**
 * 类型守卫：检查是否为非空值
 * @param value 要检查的值
 * @returns 是否为非空值
 */
export const isNotNull = <T>(value: T | null | undefined): value is T => {
  return value !== null && value !== undefined;
};
