# SeedTra 实时翻译系统 - TypeScript编码规范

**创建时间**: 2025-07-31 00:50:16  
**最后更新**: 2025-07-31 00:50:16  
**文档版本**: v1.0  
**适用范围**: 前端React + 后端NestJS

## 📋 规范概述

### 目标
- 提高代码可读性和可维护性
- 确保团队开发的一致性
- 减少潜在的运行时错误
- 提升开发效率和代码质量

### 适用范围
- 前端React应用 (TypeScript)
- 后端NestJS应用 (TypeScript)
- 共享类型定义和工具函数

## 🔧 开发环境配置

### TypeScript配置

#### tsconfig.json (后端NestJS)
```json
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2020",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "paths": {
      "@/*": ["src/*"],
      "@shared/*": ["../shared/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test"]
}
```

#### tsconfig.json (前端React)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@components/*": ["components/*"],
      "@hooks/*": ["hooks/*"],
      "@utils/*": ["utils/*"],
      "@types/*": ["types/*"],
      "@shared/*": ["../shared/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}
```

### ESLint配置

#### .eslintrc.js
```javascript
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    '@typescript-eslint/recommended',
    '@typescript-eslint/recommended-requiring-type-checking',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'error',
    '@typescript-eslint/explicit-module-boundary-types': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/prefer-const': 'error',
    '@typescript-eslint/no-inferrable-types': 'off',
  },
};
```

### Prettier配置

#### .prettierrc
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 📝 命名规范

### 文件命名
```typescript
// ✅ 正确 - kebab-case
audio-gateway.service.ts
translation-orchestration.service.ts
microphone-processor.hook.ts

// ✅ 正确 - PascalCase (组件)
AudioInputButton.tsx
TranslationPage.tsx
SourceTextView.tsx

// ❌ 错误
audioGateway.service.ts
translation_orchestration.service.ts
```

### 变量命名
```typescript
// ✅ 正确 - camelCase
const audioChunkSize: number = 3200;
const isRecording: boolean = false;
const translationResult: string = '';

// ✅ 正确 - 常量使用 SCREAMING_SNAKE_CASE
const MAX_RETRY_ATTEMPTS: number = 3;
const DEFAULT_SAMPLE_RATE: number = 16000;
const API_ENDPOINTS = {
  ASR_WEBSOCKET: 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async',
  LLM_HTTP: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
} as const;

// ❌ 错误
const AudioChunkSize: number = 3200;
const is_recording: boolean = false;
```

### 函数命名
```typescript
// ✅ 正确 - 动词开头，描述性强
function processAudioChunk(chunk: ArrayBuffer): void { }
function validateUserToken(token: string): boolean { }
function triggerTranslation(text: string): Promise<void> { }

// ✅ 正确 - 布尔值返回函数使用 is/has/can 前缀
function isValidAudioFormat(format: string): boolean { }
function hasActiveSession(userId: string): boolean { }
function canStartTranslation(): boolean { }

// ❌ 错误
function chunk(data: ArrayBuffer): void { }
function validate(token: string): boolean { }
function translation(text: string): Promise<void> { }
```

### 类和接口命名
```typescript
// ✅ 正确 - PascalCase
class AudioGateway { }
class TranslationOrchestrationService { }
interface ASRResult { }
interface TranslationChunk { }

// ✅ 正确 - 接口使用描述性名称，不使用 I 前缀
interface WebSocketConnection {
  id: string;
  userId: string;
  isActive: boolean;
}

// ✅ 正确 - 类型别名使用 Type 后缀
type AudioConfigType = {
  sampleRate: number;
  channels: number;
  bitsPerSample: number;
};

// ❌ 错误
interface IWebSocketConnection { }
class audioGateway { }
type AudioConfig = string; // 不够描述性
```

## 🏗️ 代码结构规范

### 导入顺序
```typescript
// ✅ 正确的导入顺序
// 1. Node.js 内置模块
import { createReadStream } from 'fs';
import { join } from 'path';

// 2. 第三方库
import { Injectable, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';
import fetch from 'node-fetch';

// 3. 项目内部模块 (绝对路径)
import { TranslationOrchestrationService } from '@/translation/translation.orchestration.service';
import { ConfigService } from '@/config/config.service';

// 4. 相对路径导入
import { ASRClient } from './asr.client';
import { LLMClient } from './llm.client';

// 5. 类型导入 (分组在最后)
import type { ASRResult, TranslationChunk } from '@shared/types';
import type { WebSocketConnection } from './types';
```

### 类结构顺序
```typescript
@Injectable()
export class TranslationOrchestrationService {
  // 1. 静态属性
  private static readonly MAX_RETRY_ATTEMPTS: number = 3;
  
  // 2. 实例属性
  private readonly logger: Logger = new Logger(TranslationOrchestrationService.name);
  private llmRequestControllers = new Map<string, AbortController>();
  
  // 3. 构造函数
  constructor(
    private readonly configService: ConfigService,
    private readonly asrClient: ASRClient,
    private readonly llmClient: LLMClient,
  ) {}
  
  // 4. 公共方法
  public async processAudioChunk(userId: string, audioData: ArrayBuffer): Promise<void> {
    // 实现
  }
  
  public async triggerTranslation(userId: string, text: string): Promise<void> {
    // 实现
  }
  
  // 5. 私有方法
  private async validateAudioData(data: ArrayBuffer): Promise<boolean> {
    // 实现
  }
  
  private createSseParser(): TransformStream<string, string> {
    // 实现
  }
}
```

## 🎯 类型定义规范

### 接口定义
```typescript
// ✅ 正确 - 详细的接口定义
interface ASRResult {
  readonly sessionId: string;
  readonly type: 'partial' | 'final';
  readonly text: string;
  readonly confidence: number;
  readonly timestamp: number;
  readonly startTime?: number;
  readonly endTime?: number;
  readonly words?: ReadonlyArray<ASRWord>;
}

interface ASRWord {
  readonly word: string;
  readonly confidence: number;
  readonly startTime: number;
  readonly endTime: number;
}

// ✅ 正确 - 使用泛型
interface APIResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp: number;
}

// ✅ 正确 - 联合类型
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';
type AudioFormat = 'pcm' | 'wav' | 'mp3';
```

### 枚举使用
```typescript
// ✅ 正确 - 使用 const enum 提高性能
const enum SessionStatus {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  TRANSLATING = 'translating',
  COMPLETED = 'completed',
  ERROR = 'error',
}

// ✅ 正确 - 使用 as const 创建只读对象
const API_ENDPOINTS = {
  ASR_WEBSOCKET: 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async',
  LLM_HTTP: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
} as const;

type APIEndpoint = typeof API_ENDPOINTS[keyof typeof API_ENDPOINTS];
```

### 函数类型定义
```typescript
// ✅ 正确 - 明确的函数签名
type AudioProcessor = (
  audioData: ArrayBuffer,
  sampleRate: number
) => Promise<ArrayBuffer>;

type EventHandler<T> = (event: T) => void | Promise<void>;

// ✅ 正确 - 使用泛型约束
interface Repository<T extends { id: string }> {
  findById(id: string): Promise<T | null>;
  save(entity: T): Promise<T>;
  delete(id: string): Promise<void>;
}
```

## 🛡️ 错误处理规范

### 自定义错误类
```typescript
// ✅ 正确 - 自定义错误类
export class ASRConnectionError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = 'ASRConnectionError';
  }
}

export class TranslationTimeoutError extends Error {
  constructor(
    message: string,
    public readonly timeoutMs: number
  ) {
    super(message);
    this.name = 'TranslationTimeoutError';
  }
}
```

### 错误处理模式
```typescript
// ✅ 正确 - 使用 Result 模式
type Result<T, E = Error> = {
  success: true;
  data: T;
} | {
  success: false;
  error: E;
};

async function processTranslation(text: string): Promise<Result<string, TranslationError>> {
  try {
    const result = await translateText(text);
    return { success: true, data: result };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof TranslationError ? error : new TranslationError('Unknown error')
    };
  }
}

// ✅ 正确 - 使用类型守卫
function isASRError(error: unknown): error is ASRConnectionError {
  return error instanceof ASRConnectionError;
}
```

## 📚 注释规范

### JSDoc注释
```typescript
/**
 * 处理音频块并触发语音识别
 * 
 * @param userId - 用户唯一标识符
 * @param audioData - 16kHz PCM音频数据
 * @param sequenceId - 音频块序列号
 * @returns Promise，解析为处理结果
 * 
 * @throws {ASRConnectionError} 当ASR服务连接失败时
 * @throws {InvalidAudioFormatError} 当音频格式不正确时
 * 
 * @example
 * ```typescript
 * const result = await processAudioChunk('user-123', audioBuffer, 1);
 * if (result.success) {
 *   console.log('Audio processed successfully');
 * }
 * ```
 */
public async processAudioChunk(
  userId: string,
  audioData: ArrayBuffer,
  sequenceId: number
): Promise<Result<void, ASRConnectionError | InvalidAudioFormatError>> {
  // 实现
}
```

### 行内注释
```typescript
// ✅ 正确 - 解释复杂逻辑
const sampleRateRatio = this.inputSampleRate / PCMProcessor.TARGET_SAMPLE_RATE;
// 使用线性插值进行降采样，避免混叠失真
for (let i = 0; i < inputData.length; i += sampleRateRatio) {
  const index = Math.floor(i);
  const sample = inputData[index];
  
  // 转换为16-bit PCM格式 (-32768 到 32767)
  const pcmSample = Math.max(-1, Math.min(1, sample)) * 32767;
  
  if (this.bufferIndex < this.bufferSize) {
    this.buffer[this.bufferIndex++] = pcmSample;
  }
}

// ❌ 错误 - 无意义的注释
const userId = user.id; // 获取用户ID
let count = 0; // 初始化计数器
```

## 🧪 测试规范

### 单元测试命名
```typescript
// ✅ 正确 - 描述性测试名称
describe('TranslationOrchestrationService', () => {
  describe('processAudioChunk', () => {
    it('should process valid audio chunk successfully', async () => {
      // 测试实现
    });
    
    it('should throw ASRConnectionError when ASR service is unavailable', async () => {
      // 测试实现
    });
    
    it('should handle concurrent audio chunks correctly', async () => {
      // 测试实现
    });
  });
});
```

### Mock和Stub
```typescript
// ✅ 正确 - 类型安全的Mock
const mockASRClient = {
  connect: jest.fn().mockResolvedValue(undefined),
  sendAudioChunk: jest.fn().mockResolvedValue(undefined),
  disconnect: jest.fn().mockResolvedValue(undefined),
} as jest.Mocked<ASRClient>;

// ✅ 正确 - 使用工厂函数创建测试数据
function createMockASRResult(overrides: Partial<ASRResult> = {}): ASRResult {
  return {
    sessionId: 'test-session-123',
    type: 'final',
    text: '测试文本',
    confidence: 0.95,
    timestamp: Date.now(),
    ...overrides,
  };
}
```

## 🔧 工具配置

### package.json scripts
```json
{
  "scripts": {
    "build": "tsc",
    "start": "node dist/main",
    "start:dev": "nest start --watch",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "format": "prettier --write \"src/**/*.ts\"",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "type-check": "tsc --noEmit"
  }
}
```

### Git Hooks (husky)
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm run type-check && npm run test"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

---

**维护说明**: 本编码规范是团队开发的重要标准，所有团队成员都应严格遵循。规范的修改需要经过团队讨论和一致同意。

*文档版本: v1.0*  
*创建时间: 2025-07-31 00:50:16*
