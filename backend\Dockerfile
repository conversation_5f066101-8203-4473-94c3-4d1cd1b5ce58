# SeedTra 后端服务 Dockerfile

# 基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 开发阶段
FROM base AS development

# 安装所有依赖 (包括开发依赖)
RUN npm ci

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3001 9229

# 开发命令
CMD ["npm", "run", "start:dev"]

# 构建阶段
FROM base AS build

# 安装所有依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 设置工作目录
WORKDIR /app

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 复制 package 文件
COPY package*.json ./

# 只安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 从构建阶段复制构建产物
COPY --from=build --chown=nestjs:nodejs /app/dist ./dist

# 切换到非 root 用户
USER nestjs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# 生产命令
CMD ["node", "dist/main"]
