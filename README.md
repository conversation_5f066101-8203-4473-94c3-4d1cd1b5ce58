# SeedTra 实时翻译系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)

## 🎯 项目简介

SeedTra是一个高性能、低延迟的实时翻译系统，专为语音到文本的实时翻译场景设计。系统采用现代化的技术栈，实现端到端延迟小于500毫秒的流畅翻译体验。

### 核心特性

- 🎤 **实时语音识别**: 基于豆包ASR的流式语音识别
- 🔄 **智能翻译触发**: 智能判断翻译时机，避免频繁调用
- ⚡ **流式翻译**: 基于豆包LLM的流式文本翻译
- 🎨 **实时UI渲染**: 流畅的用户界面实时更新
- 🔊 **高性能音频处理**: AudioWorklet独立线程音频处理
- 📱 **响应式设计**: 支持多设备访问

### 技术亮点

- **端到端延迟**: < 500ms
- **并发支持**: 1000+ 用户
- **系统可用性**: 99.9%
- **音频处理**: 16kHz PCM实时处理
- **架构模式**: 分布式Redis架构，支持水平扩展

## 🏗️ 技术架构

### 技术栈

| 层级 | 技术选择 | 版本 |
|------|----------|------|
| 前端框架 | React + TypeScript | 18.x |
| 构建工具 | Vite | 5.x |
| 后端框架 | NestJS + TypeScript | 10.x |
| 数据库 | PostgreSQL | 15.x |
| 缓存 | Redis | 7.x |
| 实时通信 | Socket.IO | 4.x |
| 音频处理 | AudioWorklet | - |
| 容器化 | Docker + Docker Compose | - |

### 系统架构

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   React 前端    │ ←──────────────→ │   NestJS 后端   │
│  AudioWorklet   │                 │  WebSocket网关  │
└─────────────────┘                 └─────────────────┘
                                            │
                                    ┌───────┴───────┐
                                    │               │
                            ┌───────▼──┐    ┌──────▼──────┐
                            │豆包ASR API│    │豆包LLM API  │
                            │WebSocket  │    │HTTP Stream  │
                            └───────────┘    └─────────────┘
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- PostgreSQL >= 15.0
- Redis >= 7.0
- Docker (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/SunflowersLwtech/SeedTra.git
cd SeedTra
```

2. **安装依赖**
```bash
# 安装根目录依赖
npm install

# 安装后端依赖
cd backend && npm install

# 安装前端依赖
cd ../frontend && npm install
```

3. **环境配置**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
# 配置数据库连接、Redis连接、豆包API密钥等
```

4. **数据库初始化**
```bash
# 启动PostgreSQL和Redis (使用Docker)
docker-compose up -d postgres redis

# 运行数据库迁移
cd backend && npm run migration:run
```

5. **启动开发服务**
```bash
# 启动后端服务 (端口: 3001)
cd backend && npm run start:dev

# 启动前端服务 (端口: 5173)
cd frontend && npm run dev
```

6. **访问应用**
- 前端应用: http://localhost:5173
- 后端API: http://localhost:3001

### Docker 快速启动

```bash
# 使用Docker Compose启动完整环境
docker-compose up -d

# 查看服务状态
docker-compose ps
```

## 📁 项目结构

```
SeedTra/
├── docs/                    # 📚 项目文档
├── backend/                 # 🚀 NestJS后端服务
├── frontend/                # 🎨 React前端应用
├── shared/                  # 🤝 共享代码
├── database/                # 🗄️ 数据库相关
├── docker/                  # 🐳 Docker配置
├── scripts/                 # 📜 脚本文件
└── README.md               # 📖 项目说明
```

详细的项目结构说明请参考: [项目结构目录](./docs/项目结构目录.md)

## 📖 文档

### 开发文档
- [功能需求规格书](./docs/3-requirements/功能需求规格书.md)
- [系统架构设计](./docs/8-architecture/系统架构设计.md)
- [WebSocket接口规范](./docs/6-api/WebSocket接口规范.md)
- [TypeScript编码规范](./docs/7-standards/TypeScript编码规范.md)

### 项目管理
- [项目进度总览](./docs/2-progress/项目进度总览.md)
- [开发计划文档](./docs/4-design/开发计划文档.md)
- [任务清单管理规范](./docs/1-tasks/任务清单管理规范.md)

## 🧪 测试

```bash
# 运行后端测试
cd backend && npm run test

# 运行前端测试
cd frontend && npm run test

# 运行端到端测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 🚀 部署

### 生产环境部署

```bash
# 构建生产版本
npm run build

# 使用Docker部署
docker-compose -f docker-compose.prod.yml up -d
```

### 环境变量配置

关键环境变量说明:

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/seedtra
REDIS_URL=redis://localhost:6379

# 豆包API配置
DOUBAO_ASR_API_KEY=your_asr_api_key
DOUBAO_LLM_API_KEY=your_llm_api_key

# 应用配置
NODE_ENV=production
PORT=3001
JWT_SECRET=your_jwt_secret
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 开发规范

- 遵循 [TypeScript编码规范](./docs/7-standards/TypeScript编码规范.md)
- 提交前运行 `npm run lint` 和 `npm run test`
- 使用语义化提交信息

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [项目团队]
- 邮箱: [联系邮箱]
- 项目地址: https://github.com/SunflowersLwtech/SeedTra

## 🙏 致谢

- [豆包AI](https://www.doubao.com/) - 提供ASR和LLM服务
- [NestJS](https://nestjs.com/) - 优秀的Node.js框架
- [React](https://reactjs.org/) - 强大的前端框架
- [Socket.IO](https://socket.io/) - 实时通信解决方案

---

**注意**: 本项目目前处于开发阶段，部分功能可能尚未完全实现。请参考项目进度文档了解最新开发状态。
