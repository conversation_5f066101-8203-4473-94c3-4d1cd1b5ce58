{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "moduleResolution": "node", "declaration": true, "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "incremental": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}