import React, { useState, useEffect } from 'react';
import './App.css';

// 临时的简单组件，后续会被完整的组件替换
const App: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [systemStatus, setSystemStatus] = useState<'loading' | 'online' | 'offline'>('loading');

  // 检查后端服务状态
  useEffect(() => {
    const checkBackendStatus = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/health');
        if (response.ok) {
          setSystemStatus('online');
        } else {
          setSystemStatus('offline');
        }
      } catch (error) {
        console.error('后端服务连接失败:', error);
        setSystemStatus('offline');
      }
    };

    checkBackendStatus();
    
    // 每30秒检查一次服务状态
    const interval = setInterval(checkBackendStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    switch (systemStatus) {
      case 'online': return '#4CAF50';
      case 'offline': return '#F44336';
      default: return '#FF9800';
    }
  };

  const getStatusText = () => {
    switch (systemStatus) {
      case 'online': return '服务正常';
      case 'offline': return '服务离线';
      default: return '检查中...';
    }
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <h1>🌱 SeedTra</h1>
          <p>实时翻译系统</p>
          <div className="status-indicator">
            <span 
              className="status-dot" 
              style={{ backgroundColor: getStatusColor() }}
            ></span>
            <span className="status-text">{getStatusText()}</span>
          </div>
        </div>
      </header>

      <main className="app-main">
        <div className="welcome-section">
          <h2>欢迎使用 SeedTra 实时翻译系统</h2>
          <p>高性能、低延迟的实时语音翻译解决方案</p>
          
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🎤</div>
              <h3>实时语音识别</h3>
              <p>基于豆包ASR的流式语音识别技术</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🔄</div>
              <h3>智能翻译触发</h3>
              <p>智能判断翻译时机，避免频繁调用</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3>流式翻译</h3>
              <p>基于豆包LLM的流式文本翻译</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🎨</div>
              <h3>实时UI渲染</h3>
              <p>流畅的用户界面实时更新</p>
            </div>
          </div>

          <div className="system-info">
            <h3>系统信息</h3>
            <div className="info-grid">
              <div className="info-item">
                <strong>端到端延迟:</strong> &lt; 500ms
              </div>
              <div className="info-item">
                <strong>并发支持:</strong> 1000+ 用户
              </div>
              <div className="info-item">
                <strong>系统可用性:</strong> 99.9%
              </div>
              <div className="info-item">
                <strong>音频处理:</strong> 16kHz PCM
              </div>
            </div>
          </div>

          {systemStatus === 'offline' && (
            <div className="error-message">
              <h3>⚠️ 服务连接失败</h3>
              <p>无法连接到后端服务，请确保后端服务正在运行。</p>
              <p>启动命令: <code>npm run dev:backend</code></p>
            </div>
          )}

          {systemStatus === 'online' && (
            <div className="success-message">
              <h3>✅ 系统就绪</h3>
              <p>所有服务正常运行，可以开始使用翻译功能。</p>
              <button className="start-button" disabled>
                开始翻译 (开发中)
              </button>
            </div>
          )}
        </div>
      </main>

      <footer className="app-footer">
        <p>&copy; 2024 SeedTra Team. All rights reserved.</p>
        <p>Version 1.0.0 - Development Build</p>
      </footer>
    </div>
  );
};

export default App;
